package biz

import (
	"context"
	"fmt"
	"slices"
	"sort"
	"strconv"
	"time"

	"gitlab.futunn.com/webservices/card_service_go/internal/app/consts/premium"

	mapset "github.com/deckarep/golang-set/v2"
	"github.com/golang/protobuf/proto"
	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"gitlab.futunn.com/artifact-go/ds-attribute-market/pb/attribute"
	"gitlab.futunn.com/infra/frpc/pkg/env"
	"gitlab.futunn.com/infra/frpc/pkg/errors"
	"gitlab.futunn.com/infra/frpc/pkg/log"
	"gitlab.futunn.com/infra/frpc/pkg/metadata"
	i18nutil "gitlab.futunn.com/web_data_application/golib/util/i18n"
	logutil "gitlab.futunn.com/web_data_application/golib/util/log"
	metricutil "gitlab.futunn.com/web_data_application/golib/util/metric"
	"gitlab.futunn.com/web_data_application/golib/util/mr"
	"gitlab.futunn.com/webservices/card_service_go/internal/app/consts/brokerfirm"
	"gitlab.futunn.com/webservices/card_service_go/internal/app/consts/currency"
	durationenum "gitlab.futunn.com/webservices/card_service_go/internal/app/consts/duration"
	"gitlab.futunn.com/webservices/card_service_go/internal/app/consts/errcode"
	"gitlab.futunn.com/webservices/card_service_go/internal/app/consts/goodstype"
	"gitlab.futunn.com/webservices/card_service_go/internal/app/consts/langid"
	"gitlab.futunn.com/webservices/card_service_go/internal/app/consts/market"
	"gitlab.futunn.com/webservices/card_service_go/internal/app/consts/orderby"
	"gitlab.futunn.com/webservices/card_service_go/internal/app/consts/platform"
	"gitlab.futunn.com/webservices/card_service_go/internal/app/consts/subscription"
	"gitlab.futunn.com/webservices/card_service_go/internal/app/consts/usermarket"
	"gitlab.futunn.com/webservices/card_service_go/internal/app/model/bo"
	dbmodel "gitlab.futunn.com/webservices/card_service_go/internal/app/model/db/qt_card"
	"gitlab.futunn.com/webservices/card_service_go/internal/app/model/dto"
	"gitlab.futunn.com/webservices/card_service_go/internal/app/pb/qt_card_enum_pb"
	"gitlab.futunn.com/webservices/card_service_go/internal/app/pb/quote_goods_pb"
	"gitlab.futunn.com/webservices/card_service_go/internal/app/repo/mapper/qt_card"
)

type IQuoteGoodsBiz interface {
	GetShopQuoteGoodsList(context.Context, bo.UserInfo, *quote_goods_pb.GetShopQuoteGoodsListReq) (*quote_goods_pb.GetShopQuoteGoodsListRsp_Data, error)
	GetQuoteGoodsItemList(context.Context, bo.UserInfo, *quote_goods_pb.GetQuoteGoodsItemListReq) (*quote_goods_pb.GetQuoteGoodsItemListRsp_Data, error)
	GetQuoteGoodsDetail(context.Context, bo.UserInfo, *quote_goods_pb.GetQuoteGoodsDetailReq) (*quote_goods_pb.GetQuoteGoodsDetailRsp_Data, error)
	GetUnlockInfoList(context.Context, bo.UserInfo, *quote_goods_pb.GetUnlockInfoListReq) (*quote_goods_pb.GetUnlockInfoListRsp_Data, error)
	GetMarketInfoList(context.Context) *quote_goods_pb.GetMarketInfoListRsp_Data
	GetFreeCardInfoList(context.Context, int32) (*quote_goods_pb.GetFreeCardInfoListRsp_FreeCardInfoData, error)
	GetQuoteGoodsIdByPlatformProductId(context.Context, bo.UserInfo, *quote_goods_pb.GetQuoteGoodsIdByPlatformProductIdReq) (*quote_goods_pb.GetQuoteGoodsIdByPlatformProductIdRsp_Data, error)
	GetPlatformProductIdList(context.Context, bo.UserInfo, *quote_goods_pb.GetPlatformProductIdListReq) (*quote_goods_pb.GetPlatformProductIdListRsp_Data, error)
	GetNNAndroidGoodsList(context.Context, bo.UserInfo, *quote_goods_pb.GetNNAndroidGoodsListReq) (*quote_goods_pb.GetNNAndroidGoodsListRsp_Data, error)
	GetGooglePlayGoodsList(context.Context, bo.UserInfo, *quote_goods_pb.GetGooglePlayGoodsListReq) (*quote_goods_pb.GetGooglePlayGoodsListRsp_Data, error)
	GetCardNewIapGoodsList(ctx context.Context, userInfo bo.UserInfo, req *quote_goods_pb.GetCardNewIapGoodsListReq) (*quote_goods_pb.GetCardNewIapGoodsListRsp_IapData, error)
	GetQuoteGoodsCopywritingByGoodsId(ctx context.Context, languageCode uint32, quoteGoodsIds []int32) (map[int32][]dbmodel.QuoteGoodsCopywriting, error)
	GetQuoteGoodsItemCopywritingByGoodsItemId(ctx context.Context, languageCode uint32, quoteGoodsIds []int32, quoteGoodsItemIds []int32) (map[int32]dbmodel.QuoteGoodsCopywriting, error)
	GetInAppQuoteGoodsItemFullInfoMap(ctx context.Context, clientType uint32) (map[string]bo.QuoteGoodsItemFullInfoBo, error)
}

type quoteGoodsBiz struct {
	qtCardRepo        qt_card.IQTCardRepo
	rateBiz           IRateBiz
	quotePrivilegeBiz IQuotePrivilegeBiz
	commonBiz         ICommonBiz
}

func NewQuoteGoodsBiz(qtCardRepo qt_card.IQTCardRepo, rateBiz IRateBiz, quotePrivilegeBiz IQuotePrivilegeBiz, commonBiz ICommonBiz) IQuoteGoodsBiz {
	return &quoteGoodsBiz{
		qtCardRepo:        qtCardRepo,
		rateBiz:           rateBiz,
		quotePrivilegeBiz: quotePrivilegeBiz,
		commonBiz:         commonBiz,
	}
}

// GetShopQuoteGoodsList 查询行情商城商品列表
func (q *quoteGoodsBiz) GetShopQuoteGoodsList(ctx context.Context, userInfo bo.UserInfo, req *quote_goods_pb.GetShopQuoteGoodsListReq) (*quote_goods_pb.GetShopQuoteGoodsListRsp_Data, error) {
	results := make([]*quote_goods_pb.GetShopQuoteGoodsListRsp_QuoteGoodsListItem, 0)
	// 查询用户白名单信息
	userIsInWhiteList := q.existInWhiteList(ctx, userInfo.Uid)
	// 查询卡商品
	queryResults, err := q.qtCardRepo.QueryCombinedQuoteGoodsList(ctx, dto.QueryCombinedQuoteGoodsListDto{
		ClientType:      userInfo.ClientType,
		Variety:         req.Variety,
		MarketId:        req.MarketId,
		IsProUser:       req.IsProUser,
		IsOpenApi:       req.IsOpenApi,
		Language:        userInfo.Language,
		IsIosRenewal:    proto.Int32(int32(qt_card_enum_pb.IS_NOT_RENEWAL)),
		IsWhiteListUser: userIsInWhiteList,
	})
	if err != nil {
		metricutil.Alert("QueryCombinedQuoteGoodsList_err")
		log.Error(ctx, "QueryCombinedQuoteGoodsList_err", log.ErrorField(err), log.Any("request", req))
		return nil, errcode.ErrDBError.WithMessage("query shop quote goods list error")
	}
	if len(queryResults) == 0 {
		return &quote_goods_pb.GetShopQuoteGoodsListRsp_Data{
			List: results,
		}, nil
	}
	// 排序
	q.sortByShopQuoteGoodsList(ctx, req.GetOrder(), queryResults, userInfo.ClientType)
	var (
		userOwnQtePrivilegeMap  map[int32]bool
		userCondition           bo.UserFilterCondition
		quoteGoodsId2VersionMap = make(map[int32]dbmodel.QuoteGoodsVersionControl)
	)
	// 并行查询
	err = mr.Finish(func() error {
		// 查询用户拥有的权限
		userPrivilegeRes, innErr := q.quotePrivilegeBiz.QueryUserOwnQuotePrivilege(ctx, userInfo)
		if innErr != nil {
			metricutil.Inc("QueryUserOwnQuotePrivilege_err")
			log.Error(ctx, "QueryUserOwnQuotePrivilege_err", log.ErrorField(innErr), log.Any("request", req))
		}
		userOwnQtePrivilegeMap = userPrivilegeRes
		return nil
	}, func() error {
		// 查询过滤前置信息
		filterBo, innErr := q.getUserFilterCondition(ctx, userInfo, userIsInWhiteList)
		if innErr != nil {
			metricutil.Alert("buildQuoteGoodsListFilter_err")
			log.Error(ctx, "buildQuoteGoodsListFilter_err", log.ErrorField(innErr))
			return innErr
		}
		userCondition = filterBo
		return nil
	},
		func() error {
			// 查询版本号限制
			quoteGoodsItemIds := lo.Map(queryResults, func(item dto.CombinedQuoteGoodsListItemDto, _ int) int32 {
				return item.QuoteGoodsItemId
			})
			versionControlRes, innErr := q.qtCardRepo.QueryVersionControlListByGoodsItemIds(ctx, quoteGoodsItemIds, platform.GetOSPlatformByClientType(userInfo.ClientType))
			if innErr != nil {
				metricutil.Alert("QueryVersionControlListByQuoteGoodsIds_err")
				log.Error(ctx, "QueryVersionControlListByQuoteGoodsIds_err", log.ErrorField(innErr), log.Any("quoteGoodsItemIds", quoteGoodsItemIds))
				return innErr
			}
			quoteGoodsId2VersionMap = versionControlRes
			return nil
		},
	)
	if err != nil {
		return nil, errcode.ErrDBError.WithMessage("build quote goods filter error")
	}
	// 拥有分类排序, 构建结果
	userHave := make([]*quote_goods_pb.GetShopQuoteGoodsListRsp_QuoteGoodsListItem, 0)
	userNotHave := make([]*quote_goods_pb.GetShopQuoteGoodsListRsp_QuoteGoodsListItem, 0)
	for _, item := range queryResults {
		// 过滤不展示的行情卡商品
		if q.filterQuoteGoodsByRule(ctx, userCondition, bo.QuoteGoodsFilterCondition{
			WhiteListAccess:       item.IsWhiteListAccess,
			MainBrokerFirmId:      item.ShowInMainBrokers,
			ItemMainBrokerFirmId:  item.ItemShowInMainBrokers,
			ShowInUserMarkets:     usermarket.UserMarketBit(item.ShowInUserMarkets),
			ItemShowInUserMarkets: usermarket.UserMarketBit(item.ItemShowInUserMarkets),
			MarketId:              item.MarketId,
			QuoteGoodsId:          item.QuoteGoodsId,
			QuoteGoodsItemId:      item.QuoteGoodsItemId,
			VersionCtrl:           quoteGoodsId2VersionMap[item.QuoteGoodsItemId],
		}) {
			continue
		}
		// 构建结果
		resItem := item.Convert2ShopQuoteGoodsListItem(userInfo.ClientType, req.GetIsProUser())
		// 设置是否拥有
		if have := userOwnQtePrivilegeMap[item.QuoteGoodsId]; have {
			resItem.IsHave = proto.Int32(int32(qt_card_enum_pb.USER_HAVE))
			userHave = append(userHave, resItem)
		} else {
			resItem.IsHave = proto.Int32(int32(qt_card_enum_pb.USER_NOT_HAVE))
			userNotHave = append(userNotHave, resItem)
		}
	}
	results = slices.Concat(userNotHave, userHave)
	return &quote_goods_pb.GetShopQuoteGoodsListRsp_Data{
		List: results,
	}, nil
}

// 由于行情商品是全表查的，所以直接在这排序
func (q *quoteGoodsBiz) sortByShopQuoteGoodsList(ctx context.Context, sortType int32, queryResult []dto.CombinedQuoteGoodsListItemDto, clientType uint32) {
	isPriceOrder := sortType == int32(qt_card_enum_pb.SORT_TYPE_PRICE_DESC) || sortType == int32(qt_card_enum_pb.SORT_TYPE_PRICE_ASC)
	if isPriceOrder {
		// 统一价格用于排序
		q.handleSortPrice(ctx, clientType, queryResult)
	}
	switch sortType {
	case int32(qt_card_enum_pb.SORT_TYPE_ALL):
		sort.SliceStable(queryResult, func(i, j int) bool {
			itemI := queryResult[i]
			itemJ := queryResult[j]
			return lo.If(itemI.Sort < itemJ.Sort, true).
				ElseIf(itemI.Sort == itemJ.Sort, itemI.QuoteGoodsItemId < itemJ.QuoteGoodsItemId).
				Else(false)
		})
	case int32(qt_card_enum_pb.SORT_TYPE_PRICE_DESC):
		sort.SliceStable(queryResult, func(i, j int) bool {
			itemI := queryResult[i]
			itemJ := queryResult[j]
			return lo.If(itemI.PriceCNY.GreaterThan(itemJ.PriceCNY), true).
				ElseIf(itemI.PriceCNY.Equal(itemJ.PriceCNY), itemI.QuoteGoodsItemId < itemJ.QuoteGoodsItemId).
				Else(false)
		})
	case int32(qt_card_enum_pb.SORT_TYPE_PRICE_ASC):
		sort.SliceStable(queryResult, func(i, j int) bool {
			itemI := queryResult[i]
			itemJ := queryResult[j]
			return lo.If(itemI.PriceCNY.LessThan(itemJ.PriceCNY), true).
				ElseIf(itemI.PriceCNY.Equal(itemJ.PriceCNY), itemI.QuoteGoodsItemId < itemJ.QuoteGoodsItemId).
				Else(false)
		})
	case int32(qt_card_enum_pb.SORT_TYPE_SALES_DESC):
		sort.SliceStable(queryResult, func(i, j int) bool {
			itemI := queryResult[i]
			itemJ := queryResult[j]
			return lo.If(itemI.Sales > itemJ.Sales, true).
				ElseIf(itemI.Sort == itemJ.Sort, itemI.QuoteGoodsItemId < itemJ.QuoteGoodsItemId).
				Else(false)
		})
	case int32(qt_card_enum_pb.SORT_TYPE_TIME_DESC):
		sort.SliceStable(queryResult, func(i, j int) bool {
			return queryResult[i].QuoteGoodsItemId > queryResult[j].QuoteGoodsItemId
		})
	}
}

// 处理商城列表价格排序，需要统一价格才能进行排序
func (q *quoteGoodsBiz) handleSortPrice(ctx context.Context, clientType uint32, queryResult []dto.CombinedQuoteGoodsListItemDto) {
	destCcy := currency.CNY
	srcCCY2CNYRates, _ := q.rateBiz.BatchGetYesterdayRate(ctx, []bo.CurrencyPair{
		{Source: currency.HKD, Dest: destCcy},
		{Source: currency.USD, Dest: destCcy},
		{Source: currency.MYR, Dest: destCcy},
		{Source: currency.CAD, Dest: destCcy},
		{Source: currency.AUD, Dest: destCcy},
		{Source: currency.JPY, Dest: destCcy},
	})
	srcCCY2CNYRateMap := lo.KeyBy(srcCCY2CNYRates, func(item bo.CurrencyRate) string {
		return item.CurrencyPair.Source
	})

	for i := range queryResult {
		var (
			ccy   string
			price decimal.Decimal
		)
		switch clientType {
		case metadata.ClientTypeMMAndroid, metadata.ClientTypeNNAndroid:
			ccy = queryResult[i].AndroidCurrency
			price = queryResult[i].AndroidPrice
		case metadata.ClientTypeMMIOS, metadata.ClientTypeNNIOS:
			ccy = queryResult[i].IosCurrency
			price = queryResult[i].IosPrice
		default:
			ccy = queryResult[i].WebCurrency
			price = queryResult[i].WebPrice
		}
		rate := bo.GetDefaultCurrencyRate(bo.CurrencyPair{
			Source: ccy,
			Dest:   destCcy,
		})
		rateObj, exist := srcCCY2CNYRateMap[ccy]
		if exist && !rateObj.Rate.Equal(decimal.NewFromInt(0)) {
			rate = rateObj.Rate
		}
		queryResult[i].PriceCNY = rate.Mul(price)
	}
}

// 判断行情商品是否根据主推券商进行过滤，true表示过滤，不展示
func (q *quoteGoodsBiz) filterByUserMainBroker(showInMainBrokers uint32, mainBrokerFirmIds []int32) bool {
	if int(showInMainBrokers) >= brokerfirm.All {
		return false
	}
	// 主推券商只判断第一个
	var brokerId uint32
	if len(mainBrokerFirmIds) > 0 {
		brokerId = uint32(mainBrokerFirmIds[0])
	}
	return brokerId&showInMainBrokers == 0
}

// 根据用户归属市场过滤行情商品，true表示过滤，不展示
func (q *quoteGoodsBiz) filterByUserMarket(showInUserMarkets usermarket.UserMarketBit, userMarket attribute.UserMarket) bool {
	if showInUserMarkets >= usermarket.All {
		return false
	}

	return !showInUserMarkets.HasDsMarket(userMarket)
}

// 查询白名单，不关心error，所以只打日志
func (q *quoteGoodsBiz) existInWhiteList(ctx context.Context, uid uint64) bool {
	isInWhiteList, err := q.qtCardRepo.ExistInWhiteUidList(ctx, uid)
	if err != nil {
		metricutil.Alert("ExistInWhiteUidList_Err")
		log.Error(ctx, "ExistInWhiteUidList_Err", log.ErrorField(err), log.Uint64("uid", uid))
	}
	return isInWhiteList
}

// 查询主推券商，不关心error，所以只打日志
func (q *quoteGoodsBiz) getPlatformMainBroker(ctx context.Context, clientType uint32) []int32 {
	mainBrokerFirmIds, err := q.commonBiz.QueryPlatformMainBroker(ctx, clientType)
	if err != nil {
		metricutil.Inc("QueryPlatformMainBroker_err")
		log.Error(ctx, "QueryPlatformMainBroker_err", log.ErrorField(err), log.Uint32("client_type", clientType))
	}
	return mainBrokerFirmIds
}

// 查询用户是否开户，不关心error，所以只打日志
func (q *quoteGoodsBiz) getUserAccountIsOpen(ctx context.Context, uid uint64) bool {
	isOpen, err := q.commonBiz.QueryUserAccountIsOpen(ctx, uid)
	if err != nil {
		metricutil.Inc("QueryUserAccountIsOpen_err")
		log.Error(ctx, "QueryUserAccountIsOpen_err", log.ErrorField(err))
	}
	return isOpen
}

// 前置查询 - 查询用户过滤条件所需的信息 - 用于行情商品展示过滤
func (q *quoteGoodsBiz) getUserFilterCondition(ctx context.Context, userInfo bo.UserInfo, isWhiteListUser bool) (bo.UserFilterCondition, error) {
	filterBo := bo.UserFilterCondition{
		UserInfo:          userInfo,
		InWhiteList:       isWhiteListUser,
		MainBrokerFirmIds: []int32{},
		IsOpenAccount:     false,
	}
	// 查询主推券商
	userMainBrokerFirmHandler := func() error {
		filterBo.MainBrokerFirmIds = q.getPlatformMainBroker(ctx, userInfo.ClientType)
		return nil
	}
	// 查询是否开户
	isOpenHandler := func() error {
		filterBo.IsOpenAccount = q.getUserAccountIsOpen(ctx, userInfo.Uid)
		return nil
	}
	// 查询vip资格
	vipQualificationHandler := func() error {
		userVIPInfoData, innErr := q.quotePrivilegeBiz.QueryUserVIPInfo(ctx, userInfo.Uid, userInfo.ClientType, userInfo.ClientVersion)
		if innErr != nil {
			metricutil.Alert("QueryUserVipQualification_err")
			log.Error(ctx, "QueryUserVipQualification_err", log.ErrorField(innErr), log.Uint64("uid", userInfo.Uid))
			return nil
		}
		filterBo.UserVIPInfo = userVIPInfoData
		return nil
	}

	// 并行处理
	err := mr.Finish(userMainBrokerFirmHandler, isOpenHandler, vipQualificationHandler)
	return filterBo, err
}
const a = 
// 行情商品展示过滤
func (q *quoteGoodsBiz) filterQuoteGoodsByRule(ctx context.Context, userCondition bo.UserFilterCondition, filterCondition bo.QuoteGoodsFilterCondition) bool {
	// 白名单用户不对以下条件过滤
	if userCondition.InWhiteList {
		return false
	}
	// 判断主推券商
	if q.filterByUserMainBroker(filterCondition.MainBrokerFirmId, userCondition.MainBrokerFirmIds) {
		return true
	}
	// item判断主推券商
	if q.filterByUserMainBroker(filterCondition.ItemMainBrokerFirmId, userCondition.MainBrokerFirmIds) {
		return true
	}
	// 是否白名单商品
	if filterCondition.IsWhiteListAccess() {
		return true
	}
	// 非JP地区用户，未开户则不展示
	if !env.IsJPLoc() {
		if filterCondition.MarketId == market.JP && !userCondition.IsOpenAccount {
			return true
		}
	}
	// 判断版本号
	if q.filterByClientVersion(filterCondition.VersionCtrl, userCondition.ClientVersion) {
		return true
	}
	// 判断premium商品
	if filterCondition.IsPremiumGoods() {
		if !q.isShowPremiumGoods(ctx, userCondition) {
			return true
		}
		// 根据用户归属市场过滤
		if q.filterByUserMarket(filterCondition.ShowInUserMarkets, userCondition.GetVipUserMarket()) {
			return true
		}
		// item根据用户归属市场过滤
		if q.filterByUserMarket(filterCondition.ItemShowInUserMarkets, userCondition.GetVipUserMarket()) {
			return true
		}
	}
	// 判断totalView商品，展示premium则不展示totalView
	if filterCondition.IsTotalViewGoods() {
		if q.isShowPremiumGoods(ctx, userCondition) {
			return true
		}
	}
	return false
}

func (q *quoteGoodsBiz) isShowPremiumGoods(ctx context.Context, userCondition bo.UserFilterCondition) bool {
	// 特殊逻辑：日本归属市场不给买premium
	if userCondition.GetVipUserMarket() == attribute.UserMarket_JPMarket {
		return false
	}
	isShowPremium := q.quotePrivilegeBiz.IsShowPremiumByVIPInfo(ctx, userCondition.Uid, userCondition.UserVIPInfo)
	if !isShowPremium {
		return false
	}
	// 行情商城商品要额外判断版本号，离谱。。。
	_, supportedPremiumClientVersion := premium.GetPremiumClientInfo()
	return (!userCondition.IsMobileClient()) || userCondition.ClientVersion >= supportedPremiumClientVersion
}

// 判断版本号是否在限制范围内
func (q *quoteGoodsBiz) filterByClientVersion(versionCtrl dbmodel.QuoteGoodsVersionControl, userClientVersion uint32) bool {
	// 请求没传版本号则不过滤
	if userClientVersion == 0 {
		return false
	}
	// 没有版本号限制则不过滤
	if versionCtrl.QuoteGoodsItemId == 0 {
		return false
	}

	return (versionCtrl.LteVersion != 0 && uint32(versionCtrl.LteVersion) <= userClientVersion) ||
		(versionCtrl.GteVersion != 0 && uint32(versionCtrl.GteVersion) > userClientVersion)
}

// GetQuoteGoodsItemList 购买页查询商品列表
func (q *quoteGoodsBiz) GetQuoteGoodsItemList(ctx context.Context, userInfo bo.UserInfo, req *quote_goods_pb.GetQuoteGoodsItemListReq) (*quote_goods_pb.GetQuoteGoodsItemListRsp_Data, error) {
	result := &quote_goods_pb.GetQuoteGoodsItemListRsp_Data{
		List: make([]*quote_goods_pb.GetQuoteGoodsItemListRsp_GoodsInfo, 0),
	}
	var err error
	// 查询用户白名单信息
	userIsInWhiteList := q.existInWhiteList(ctx, userInfo.Uid)
	// 查询汇率
	result.RateConvertInfoList, err = q.getRateConvertInfoList(ctx)
	if err != nil {
		log.Error(ctx, "getRateConvertInfoList_err", log.ErrorField(err))
		metricutil.Alert("getRateConvertInfoList_err")
		return nil, errcode.ErrDBError.WithMessage("query rate error")
	}
	// 根据市场id查询行情卡商品列表
	goodsItemList, err := q.qtCardRepo.QueryCombinedQuoteGoodsItemListBy(ctx, dto.QueryCombinedQuoteGoodsItemListByDto{
		Language:        userInfo.Language,
		MarketId:        req.MarketId,
		IsProUser:       req.IsProUser,
		IsIosRenewal:    proto.Int32(int32(qt_card_enum_pb.IS_NOT_RENEWAL)),
		Channel:         req.Channel,
		ClientType:      userInfo.ClientType,
		SortBy:          proto.Int32(orderby.Asc),
		IsWhiteListUser: userIsInWhiteList,
	})
	if err != nil {
		metricutil.Alert("QueryCombinedQuoteGoodsItemListBy_err")
		log.Error(ctx, "QueryCombinedQuoteGoodsItemListBy_err", log.ErrorField(err), log.Any("request", req))
		return nil, errcode.ErrDBError.WithMessage("query quote goods item list error")
	}
	if len(goodsItemList) == 0 {
		return result, nil
	}
	// 排序
	sort.SliceStable(goodsItemList, func(i, j int) bool {
		if goodsItemList[i].Sort == goodsItemList[j].Sort {
			return goodsItemList[i].Duration < goodsItemList[j].Duration
		}
		return goodsItemList[i].Sort < goodsItemList[j].Sort
	})

	// 查询过滤前置信息
	quoteGoodsItemIds := lo.Map(goodsItemList, func(item dto.CombinedQuoteGoodsItemListItemDto, _ int) int32 {
		return item.Id
	})
	quoteGoodsId2VersionMap, err := q.qtCardRepo.QueryVersionControlListByGoodsItemIds(ctx, quoteGoodsItemIds, platform.GetOSPlatformByClientType(userInfo.ClientType))
	if err != nil {
		metricutil.Alert("QueryVersionControlListByQuoteGoodsIds_err")
		log.Error(ctx, "QueryVersionControlListByQuoteGoodsIds_err", log.ErrorField(err), log.Any("quoteGoodsItemIds", quoteGoodsItemIds))
		return nil, err
	}

	userCondition, err := q.getUserFilterCondition(ctx, userInfo, userIsInWhiteList)
	if err != nil {
		metricutil.Alert("buildQuoteGoodsListFilter_err")
		log.Error(ctx, "buildQuoteGoodsListFilter_err", log.ErrorField(err))
		return nil, errcode.ErrDBError.WithMessage("handle quote goods item list limit rule error")
	}
	for i := range goodsItemList {
		item := goodsItemList[i]
		// 过滤不展示的行情卡商品
		if q.filterQuoteGoodsByRule(ctx, userCondition, bo.QuoteGoodsFilterCondition{
			WhiteListAccess:       item.IsWhiteListAccess,
			MainBrokerFirmId:      item.ShowInMainBrokers,
			ItemMainBrokerFirmId:  item.ItemShowInMainBrokers,
			ShowInUserMarkets:     usermarket.UserMarketBit(item.ShowInUserMarkets),
			ItemShowInUserMarkets: usermarket.UserMarketBit(item.ItemShowInUserMarkets),
			MarketId:              item.MarketId,
			QuoteGoodsId:          item.QuoteGoodsId,
			QuoteGoodsItemId:      item.Id,
			VersionCtrl:           quoteGoodsId2VersionMap[item.Id],
		}) {
			continue
		}
		// 处理微信价格
		innErr := q.calculateWeChatGoodsPrice(ctx, &item)
		if innErr != nil {
			metricutil.Alert("calculateWeChatGoodsPrice_err")
			log.Error(ctx, "calculateWeChatGoodsPrice_err", log.ErrorField(innErr), log.Int32("quoteGoodsItemId", item.Id), log.Int32("quoteGoodsId", item.QuoteGoodsId))
			continue
		}
		finalItem := item.Convert2GetQuoteGoodsItemListItem(userInfo.ClientType, userInfo.Language)
		result.List = append(result.List, finalItem)
	}
	return result, nil
}

// 获取默认的汇率信息
func (q *quoteGoodsBiz) getDefaultRateConvertInfoList() []*quote_goods_pb.RateConvertInfo {
	defaultRates := bo.GetDefaultCurrencyRates()
	result := make([]*quote_goods_pb.RateConvertInfo, 0, len(defaultRates))
	for _, item := range defaultRates {
		result = append(result, &quote_goods_pb.RateConvertInfo{
			SourceCurrency: proto.String(item.CurrencyPair.Source),
			DestCurrency:   proto.String(item.CurrencyPair.Dest),
			Rate:           proto.String(item.Rate.String()),
		})
	}
	return result
}

// 补充汇率信息
func (q *quoteGoodsBiz) getRateConvertInfoList(ctx context.Context) ([]*quote_goods_pb.RateConvertInfo, error) {
	result := q.getDefaultRateConvertInfoList()
	currencyPairs := lo.Map(result, func(item *quote_goods_pb.RateConvertInfo, _ int) bo.CurrencyPair {
		return bo.CurrencyPair{
			Source: item.GetSourceCurrency(),
			Dest:   item.GetDestCurrency(),
		}
	})
	// 汇率查询忽略错误，使用默认值兜底
	rates, _ := q.rateBiz.BatchGetYesterdayRate(ctx, currencyPairs)
	for i, item := range result {
		for _, rate := range rates {
			if rate.CurrencyPair.Source == item.GetSourceCurrency() &&
				rate.CurrencyPair.Dest == item.GetDestCurrency() &&
				!rate.Rate.Equal(decimal.NewFromInt(0)) {
				result[i].Rate = proto.String(rate.Rate.String())
				break
			}
		}
	}
	return result, nil
}

// 计算微信平台上行情卡商品的价格
func (q *quoteGoodsBiz) calculateWeChatGoodsPrice(ctx context.Context, item *dto.CombinedQuoteGoodsItemListItemDto) error {
	webRate, err := q.rateBiz.GetYesterdayRate(ctx, bo.CurrencyPair{Source: item.WebCurrency, Dest: currency.CNY})
	if err != nil {
		return errors.WithMessage(err, "GetYesterdayWebCurrencyToCnyRateInCache err")
	}
	item.WxWebPrice = item.WebPrice.Mul(webRate).RoundCeil(2)
	item.WxWebOriginalPrice = item.WebOriginalPrice.Mul(webRate).RoundCeil(2)
	androidRate, err := q.rateBiz.GetYesterdayRate(ctx, bo.CurrencyPair{Source: item.AndroidCurrency, Dest: currency.CNY})
	if err != nil {
		return errors.WithMessage(err, "GetYesterdayAndroidCurrencyToCnyRateInCache err")
	}
	item.WxAndroidPrice = item.AndroidPrice.Mul(androidRate).RoundCeil(2)
	item.WxAndroidOriginalPrice = item.AndroidOriginalPrice.Mul(androidRate).RoundCeil(2)
	iosRate, err := q.rateBiz.GetYesterdayRate(ctx, bo.CurrencyPair{Source: item.IosCurrency, Dest: currency.CNY})
	if err != nil {
		return errors.WithMessage(err, "GetYesterdayIosCurrencyToCnyRateInCache err")
	}
	item.WxIosPrice = item.IosPrice.Mul(iosRate).RoundCeil(2)
	item.WxIosOriginalPrice = item.IosOriginalPrice.Mul(iosRate).RoundCeil(2)
	return nil
}

// GetQuoteGoodsDetail 查询商品详情信息
func (q *quoteGoodsBiz) GetQuoteGoodsDetail(ctx context.Context, userInfo bo.UserInfo, req *quote_goods_pb.GetQuoteGoodsDetailReq) (*quote_goods_pb.GetQuoteGoodsDetailRsp_Data, error) {
	queryResult, err := q.qtCardRepo.QueryOneQuoteGoodsWithCopywritingBy(ctx, dto.QueryQuoteGoodsWithCopywritingDto{
		// 跟老版本保持一致，不根据client_type过滤
		Language:       userInfo.Language,
		Id:             req.GetQuoteGoodsId(),
		IsProfessional: req.IsProfessional,
	}, qt_card.QuoteGoodsWithCopywritingQueryFields)
	if err != nil && !q.qtCardRepo.IsRecordNotFound(err) {
		metricutil.Alert("QueryQuoteGoodsWithCopywriting_err")
		log.Error(ctx, "QueryQuoteGoodsWithCopywriting_err", log.ErrorField(err), log.Any("request", req))
		return nil, errcode.ErrDBError.WithMessage("query quote goods with copywriting error")
	}
	if q.qtCardRepo.IsRecordNotFound(err) {
		return nil, errcode.ErrNotFoundError
	}
	return queryResult.Convert2QuoteGoodsDetail(userInfo.ClientType), nil
}

// GetUnlockInfoList 查询财务解读解锁列表
func (q *quoteGoodsBiz) GetUnlockInfoList(ctx context.Context, userInfo bo.UserInfo, req *quote_goods_pb.GetUnlockInfoListReq) (*quote_goods_pb.GetUnlockInfoListRsp_Data, error) {
	result := &quote_goods_pb.GetUnlockInfoListRsp_Data{
		List: make([]*quote_goods_pb.GetUnlockInfoListRsp_UnlockInfoListItem, 0),
	}
	area, err := userInfo.GetArea(ctx)
	if err != nil {
		metricutil.Inc("GetArea_err")
		log.Error(ctx, "GetArea_err", log.ErrorField(err), log.String("ip", userInfo.Ip.String()))
		area = int32(qt_card_enum_pb.AREA_OVERSEA)
	}
	queryResults, err := q.qtCardRepo.QueryUnlockInfoListBy(ctx, dto.QueryUnlockInfoListByDto{
		FuncType:          req.FuncType,
		Area:              &area,
		ClientType:        &userInfo.ClientType,
		ClientVersion:     &userInfo.ClientVersion,
		SortByWeightOrder: proto.Int32(orderby.Asc),
	})
	if err != nil {
		metricutil.Alert("QueryUnlockInfoListBy_err")
		log.Error(ctx, "QueryUnlockInfoListBy_err", log.ErrorField(err), log.Int32("funcType", req.GetFuncType()))
		return nil, errcode.ErrDBError.WithMessage("query unlock info list error")
	}
	for _, item := range queryResults {
		finalItem := dto.Convert2RspUnlockInfoListItem(item, userInfo.Language)
		result.List = append(result.List, finalItem)
	}
	return result, nil
}

// GetMarketInfoList 拉取市场信息列表
func (q *quoteGoodsBiz) GetMarketInfoList(ctx context.Context) *quote_goods_pb.GetMarketInfoListRsp_Data {
	marketEnumList := market.GetMarketEnum()
	result := &quote_goods_pb.GetMarketInfoListRsp_Data{}
	for mkt, desc := range marketEnumList {
		result.MarketInfoList = append(result.MarketInfoList, &quote_goods_pb.GetMarketInfoListRsp_MarketInfoItem{
			MarketId: proto.Int32(mkt),
			Desc:     proto.String(desc),
		})
	}
	// 排序保证响应结果稳定
	sort.SliceStable(result.MarketInfoList, func(i, j int) bool {
		return result.MarketInfoList[i].GetMarketId() < result.MarketInfoList[j].GetMarketId()
	})
	return result
}

// GetFreeCardInfoList 拉取行情卡商品信息
func (q *quoteGoodsBiz) GetFreeCardInfoList(ctx context.Context, marketId int32) (*quote_goods_pb.GetFreeCardInfoListRsp_FreeCardInfoData, error) {
	// 查询商品spu
	quoteGoodsList, err := q.qtCardRepo.QueryQuoteGoodsList(ctx, dto.QueryQuoteGoodsDto{
		MarketId:          proto.Int32(marketId),
		IsWhiteListAccess: proto.Int32(int32(qt_card_enum_pb.IS_NOT_WHITE_LIST_ACCESS)),
	}, qt_card.FreeCardInfoQuoteGoodsFields)
	if err != nil {
		log.Error(ctx, "QueryQuoteGoodsList_err", log.ErrorField(err), log.Int32("marketId", marketId))
		metricutil.Alert("QueryQuoteGoodsList_err")
		return nil, errcode.ErrDBError.WithMessage("failed to query quote goods")
	}
	quoteGoodsIds := lo.Map(quoteGoodsList, func(item dbmodel.QuoteGoods, _ int) int32 {
		return item.Id
	})
	// 查询商品sku
	goodsItems, err := q.qtCardRepo.QueryQuoteGoodsItemListBy(ctx, dto.QueryQuoteGoodsItemListByDto{
		QuoteGoodsIds: quoteGoodsIds,
		IsRenewal:     lo.Ternary(platform.IsNN(), proto.Int32(int32(qt_card_enum_pb.IS_NOT_RENEWAL)), nil),
	}, qt_card.FreeCardInfoQuoteGoodsItemFields)
	if err != nil {
		log.Error(ctx, "QueryQuoteGoodsItemListBy_err", log.ErrorField(err), log.Int32("marketId", marketId))
		metricutil.Alert("QueryQuoteGoodsItemListBy_err")
		return nil, errcode.ErrDBError.WithMessage("failed to query quote goods items")
	}
	quoteGoodsId2ItemListMap := lo.GroupBy(goodsItems, func(item dbmodel.QuoteGoodsItem) int32 {
		return item.QuoteGoodsId
	})
	// 查询文案
	copywritings, err := q.qtCardRepo.QueryQuoteGoodsCopywriting(ctx, dto.QueryQuoteGoodsCopywritingDto{
		QuoteGoodsIds:  quoteGoodsIds,
		IsProfessional: proto.Int32(int32(qt_card_enum_pb.IS_NOT_PROFESSIONAL)),
	}, qt_card.NormalQuoteGoodsCopywritingFields)
	if err != nil {
		log.Error(ctx, "QueryQuoteGoodsCopywriting_err", log.ErrorField(err), log.Int32("marketId", marketId))
		metricutil.Alert("QueryQuoteGoodsCopywriting_err")
		return nil, errcode.ErrDBError.WithMessage("failed to query quote goods copywriting")
	}
	quoteGoodsId2CopyWritingListMap := lo.GroupBy(copywritings, func(item dbmodel.QuoteGoodsCopywriting) int32 {
		return item.QuoteGoodsId
	})
	result := &quote_goods_pb.GetFreeCardInfoListRsp_FreeCardInfoData{
		Platform: proto.String(lo.Ternary(platform.IsNN(), "牛牛", "moomoo")),
	}
	freeCardInfoList := make([]*quote_goods_pb.GetFreeCardInfoListRsp_FreeCardInfoItem, 0, len(quoteGoodsList))
	for _, quoteGoods := range quoteGoodsList {
		// 只支持部分券商的需要排除掉
		if int(quoteGoods.ShowInMainBrokers) < brokerfirm.All {
			continue
		}
		// premium暂时不给奖励系统下发
		if quoteGoods.Id == goodstype.Premium {
			continue
		}
		tmpCopywritings := quoteGoodsId2CopyWritingListMap[quoteGoods.Id]
		tmpGoodsPrices := quoteGoodsId2ItemListMap[quoteGoods.Id]
		if len(tmpCopywritings) == 0 || len(tmpGoodsPrices) == 0 {
			continue
		}
		freeCardInfoItem := q.buildFreeCardInfoItem(quoteGoods, tmpCopywritings, tmpGoodsPrices)
		freeCardInfoList = append(freeCardInfoList, freeCardInfoItem)
	}
	result.FreeCardInfoList = freeCardInfoList
	return result, nil
}

// 构建FreeCardInfo查询结果
func (q *quoteGoodsBiz) buildFreeCardInfoItem(quoteGoods dbmodel.QuoteGoods, copywritings []dbmodel.QuoteGoodsCopywriting, goodsItemList []dbmodel.QuoteGoodsItem) *quote_goods_pb.GetFreeCardInfoListRsp_FreeCardInfoItem {
	cardNames := lo.Map(copywritings, func(item dbmodel.QuoteGoodsCopywriting, _ int) *quote_goods_pb.GetFreeCardInfoListRsp_CardName {
		return &quote_goods_pb.GetFreeCardInfoListRsp_CardName{
			Name:     proto.String(item.Title),
			Language: proto.Int32(int32(item.Language)),
		}
	})

	priceInfos := make([]*quote_goods_pb.GetFreeCardInfoListRsp_FreeCardPriceInfo, 0, len(goodsItemList))
	for _, v := range goodsItemList {
		priceInfos = append(priceInfos, &quote_goods_pb.GetFreeCardInfoListRsp_FreeCardPriceInfo{
			Professional: proto.Int32(int32(v.IsProfessional)),
			Duration:     proto.Int32(v.Duration),
			DurationType: proto.Int32(int32(qt_card_enum_pb.DURATION_TYPE_MONTH)),
			Currency:     proto.String(v.WebCurrency),
			Price:        proto.String(v.WebPrice.String()),
		})
		// 用月卡的价钱计算出天卡的价钱
		if v.Duration == durationenum.Month {
			priceInfos = append(priceInfos, &quote_goods_pb.GetFreeCardInfoListRsp_FreeCardPriceInfo{
				Professional: proto.Int32(int32(v.IsProfessional)),
				Duration:     proto.Int32(v.Duration),
				DurationType: proto.Int32(int32(qt_card_enum_pb.DURATION_TYPE_DAY)),
				Currency:     proto.String(v.WebCurrency),
				Price:        proto.String(v.WebPrice.Div(decimal.NewFromFloat(30)).StringFixed(2)),
			})
		}
	}
	return &quote_goods_pb.GetFreeCardInfoListRsp_FreeCardInfoItem{
		GoodsType:     proto.Int32(quoteGoods.Id),
		MarketId:      proto.Int32(int32(quoteGoods.MarketId)),
		CardNames:     cardNames,
		PriceInfoList: priceInfos,
	}
}

// GetQuoteGoodsIdByPlatformProductId 客户端根据第三方平台商品id查询行情卡商品id
func (q *quoteGoodsBiz) GetQuoteGoodsIdByPlatformProductId(ctx context.Context, userInfo bo.UserInfo, req *quote_goods_pb.GetQuoteGoodsIdByPlatformProductIdReq) (*quote_goods_pb.GetQuoteGoodsIdByPlatformProductIdRsp_Data, error) {
	id, err := q.qtCardRepo.QueryQuoteGoodsIdByPlatformProductId(ctx, dto.QueryQuoteGoodsIdByPlatformProductIdDto{
		PlatformProductId: req.GetProductId(),
		ClientType:        userInfo.ClientType,
	})
	if err != nil && !q.qtCardRepo.IsRecordNotFound(err) {
		metricutil.Alert("QueryQuoteGoodsIdByPlatformProductId_err")
		log.Error(ctx, "QueryQuoteGoodsIdByPlatformProductId_err", log.ErrorField(err), log.String("platformProductId", req.GetProductId()), log.Uint32("clientType", userInfo.ClientType))
		return nil, errcode.ErrDBError.WithMessage("query quote goods id error")
	}
	if q.qtCardRepo.IsRecordNotFound(err) {
		return nil, errcode.ErrNotFoundError
	}
	return &quote_goods_pb.GetQuoteGoodsIdByPlatformProductIdRsp_Data{
		GoodsType: proto.Int32(id),
	}, nil
}

// GetPlatformProductIdList 查询第三方商品id列表
func (q *quoteGoodsBiz) GetPlatformProductIdList(ctx context.Context, userInfo bo.UserInfo, request *quote_goods_pb.GetPlatformProductIdListReq) (*quote_goods_pb.GetPlatformProductIdListRsp_Data, error) {
	ids, err := q.qtCardRepo.QueryAllPlatformProductIdList(ctx, dto.QueryAllPlatformProductIdListDto{ClientType: userInfo.ClientType})
	if err != nil {
		metricutil.Alert("QueryAllPlatformProductIdList_err")
		log.Error(ctx, "QueryAllPlatformProductIdList_err", log.ErrorField(err), log.Uint32("clientType", userInfo.ClientType))
		return nil, errcode.ErrDBError.WithMessage("query all platform product id list error")
	}
	// 去重
	ids = lo.Uniq(ids)
	return &quote_goods_pb.GetPlatformProductIdListRsp_Data{
		AllProductionIDList: ids,
		NextFreshTime:       proto.Int64(time.Now().Unix() + 3600),
	}, nil
}

// GetNNAndroidGoodsList niuniu android 查询商品列表
func (q *quoteGoodsBiz) GetNNAndroidGoodsList(ctx context.Context, userInfo bo.UserInfo, req *quote_goods_pb.GetNNAndroidGoodsListReq) (*quote_goods_pb.GetNNAndroidGoodsListRsp_Data, error) {
	var (
		nnAndroidGoods = &quote_goods_pb.GetNNAndroidGoodsListRsp_Data{}
		itemCurrency   = currency.HKD
	)
	quoteGoods, quoteGoodsItemList, err := q.getQuoteGoodsWithItemListByGoodsId(ctx, bo.GetQuoteGoodsItemListByBo{
		UserInfo:     userInfo,
		QuoteGoodsId: req.GetGoodsType(),
		IsProUser:    req.GetIsProUser(),
		IsRenewal:    proto.Int32(int32(qt_card_enum_pb.IS_NOT_RENEWAL)),
	})
	if err != nil {
		return nil, err
	}
	goodsList := make([]*quote_goods_pb.GetNNAndroidGoodsListRsp_Data_GoodsInfo, 0, len(quoteGoodsItemList))
	for _, item := range quoteGoodsItemList {
		finalItem := dto.Convert2GetNNAndroidGoodsListDataGoodsInfo(item, userInfo.Language)
		ccyInfo, innErr := q.handleNNAndroidGoodsListItemPriceInfo(ctx, userInfo.Language, item, finalItem)
		if innErr != nil {
			log.Error(ctx, "handleNNAndroidGoodsListItemPriceInfo_err", log.ErrorField(innErr), log.Int32("quoteGoodsId", req.GetGoodsType()), log.Int32("quoteGoodsItemId", item.Id))
			continue
		}
		itemCurrency = ccyInfo.GoodsCurrency
		goodsList = append(goodsList, finalItem)
	}
	nnAndroidGoods, err = q.buildGetNNAndroidGoodsListRsp(ctx, userInfo, itemCurrency, quoteGoods)
	if err != nil {
		metricutil.Alert("buildGetNNAndroidGoodsListRsp_err")
		log.Error(ctx, "buildGetNNAndroidGoodsListRsp_err", log.ErrorField(err), log.Int32("quoteGoodsId", req.GetGoodsType()), log.Int32("isProUser", req.GetIsProUser()))
		return nil, errcode.ErrInternalServerError.WithMessage("build response error")
	}
	nnAndroidGoods.GoodsList = goodsList
	return nnAndroidGoods, nil
}

// 处理 niuniu android 商品价格信息
func (q *quoteGoodsBiz) handleNNAndroidGoodsListItemPriceInfo(ctx context.Context, language uint32, quoteGoodsItem dbmodel.QuoteGoodsItem, finalItem *quote_goods_pb.GetNNAndroidGoodsListRsp_Data_GoodsInfo) (*bo.NNAndroidGoodsPriceBo, error) {
	currencyInfo := &bo.NNAndroidGoodsPriceBo{}
	var platformProductId *string
	var cnyPriceInfo *quote_goods_pb.GetNNAndroidGoodsListRsp_Data_PriceInfo
	priceList := make([]*quote_goods_pb.GetNNAndroidGoodsListRsp_Data_PriceInfo, 0)

	price := quoteGoodsItem.AndroidPrice
	originalPrice := quoteGoodsItem.AndroidOriginalPrice
	ccy := quoteGoodsItem.AndroidCurrency
	// 安卓还得hard code，追加一个cny
	toCnyRate, err := q.rateBiz.GetYesterdayRate(ctx, bo.CurrencyPair{Source: ccy, Dest: currency.CNY})
	if err != nil {
		return nil, errors.WithMessage(err, fmt.Sprintf("get %s to cny rate error", ccy))
	}
	cnyPriceInfo = &quote_goods_pb.GetNNAndroidGoodsListRsp_Data_PriceInfo{
		Id:                     proto.String(strconv.Itoa(int(quoteGoodsItem.Id))),
		Price:                  proto.Int32(int32(price.Mul(toCnyRate).RoundCeil(2).Mul(decimal.NewFromInt32(100)).IntPart())),         // 乘了汇率，转成分
		OriginPrice:            proto.Int32(int32(originalPrice.Mul(toCnyRate).RoundCeil(2).Mul(decimal.NewFromInt32(100)).IntPart())), // 乘了汇率，转成分
		CurrencyConversionTips: proto.String(i18nutil.GetI18nFormatByLangCode(language, langid.StringID234232, price.Mul(toCnyRate).RoundCeil(2).String())),
		CurrencyDesc:           proto.String(currency.GetCurrencyDesc(language, currency.CNY)),
		Currency:               proto.Int32(int32(qt_card_enum_pb.CURRENCY_CODE_CNY)),
	}
	finalItem.CNY = cnyPriceInfo
	priceList = append(priceList, cnyPriceInfo)
	// 转成USD支付
	if q.commonBiz.ShouldConvertToUSD(ccy) {
		ccy2usdRate, innErr := q.rateBiz.GetYesterdayRate(ctx, bo.CurrencyPair{Source: ccy, Dest: currency.USD})
		if innErr != nil {
			return nil, errors.WithMessage(innErr, "get ccy to usd rate error")
		}
		priceList = append(priceList, &quote_goods_pb.GetNNAndroidGoodsListRsp_Data_PriceInfo{
			Id:                     proto.String(strconv.Itoa(int(quoteGoodsItem.Id))),
			Price:                  proto.Int32(int32(price.Mul(ccy2usdRate).RoundCeil(2).Mul(decimal.NewFromInt32(100)).IntPart())),         // 乘了汇率, 转成分
			OriginPrice:            proto.Int32(int32(originalPrice.Mul(ccy2usdRate).RoundCeil(2).Mul(decimal.NewFromInt32(100)).IntPart())), // 乘了汇率，转成分
			CurrencyConversionTips: proto.String(i18nutil.GetI18nFormatByLangCode(language, langid.StringID234040, price.Mul(ccy2usdRate).RoundCeil(2).String())),
			CurrencyDesc:           proto.String(currency.GetCurrencyDesc(language, currency.USD)),
			Currency:               proto.Int32(int32(qt_card_enum_pb.CURRENCY_CODE_USD)),
		})
	}
	currencyPriceInfo := &quote_goods_pb.GetNNAndroidGoodsListRsp_Data_PriceInfo{
		Id:           proto.String(strconv.Itoa(int(quoteGoodsItem.Id))),
		Price:        proto.Int32(int32(price.Mul(decimal.NewFromInt32(100)).IntPart())),
		OriginPrice:  proto.Int32(int32(originalPrice.Mul(decimal.NewFromInt32(100)).IntPart())),
		CurrencyDesc: proto.String(currency.GetCurrencyDesc(language, ccy)),
		Currency:     proto.Int32(currency.GetCurrencyCode(ccy)),
		IapProductId: platformProductId,
	}
	priceList = append(priceList, currencyPriceInfo)
	switch ccy {
	case currency.HKD:
		finalItem.HKD = currencyPriceInfo
	case currency.CNY:
		finalItem.CNY = cnyPriceInfo
	case currency.USD:
		finalItem.USD = currencyPriceInfo
	case currency.MYR:
		finalItem.MYR = currencyPriceInfo
	}
	finalItem.PriceInfoList = priceList
	currencyInfo.GoodsCurrency = ccy
	return currencyInfo, nil
}

// 构建niuniu android 商品列表查询结果
func (q *quoteGoodsBiz) buildGetNNAndroidGoodsListRsp(ctx context.Context, userInfo bo.UserInfo, itemCurrency string, queryResult *dto.QuoteGoodsWithCopywritingDto) (*quote_goods_pb.GetNNAndroidGoodsListRsp_Data, error) {
	supportCurrencySet := mapset.NewSet[int32]()
	area, err := userInfo.GetArea(ctx)
	if err != nil && !errors.Is(err, errcode.ErrParameterInvalid) {
		return nil, errors.WithMessage(err, "GetArea error")
	}
	if errors.Is(err, errcode.ErrParameterInvalid) {
		area = int32(qt_card_enum_pb.AREA_HK)
	}
	nnAndroidGoods := queryResult.Convert2QuoteGoodsWithItemListInAppRsp(userInfo.Language, area)
	rate, err := q.rateBiz.GetYesterdayRate(ctx, bo.CurrencyPair{Source: itemCurrency, Dest: currency.CNY})
	if err != nil {
		return nil, err
	}
	finalRate, _ := rate.Float64()
	buyStatementStr := q.buildAppWeChatBuyStatement(userInfo.Language, itemCurrency, rate)
	if itemCurrency == currency.HKD || itemCurrency == currency.USD {
		supportCurrencySet.Add(currency.GetCurrencyCode(itemCurrency))
	}
	if q.commonBiz.ShouldConvertToUSD(itemCurrency) {
		supportCurrencySet.Add(int32(qt_card_enum_pb.CURRENCY_CODE_USD))
	}

	showCurrency := currency.GetCurrencyCode(itemCurrency)
	nnAndroidGoods.SetBuyStatementWechat(buyStatementStr)
	nnAndroidGoods.SetRate(finalRate)
	nnAndroidGoods.SetIsCn(int32(qt_card_enum_pb.IS_NOT_CN))
	if env.IsDevEnv() || area == int32(qt_card_enum_pb.AREA_MAIN_LAND) {
		nnAndroidGoods.SetIsCn(int32(qt_card_enum_pb.IS_CN))
	}
	nnAndroidGoods.SetTypeDesc(queryResult.Title)
	nnAndroidGoods.SetMiniIconUrl(queryResult.MiniIconUrl)
	nnAndroidGoods.SetShowCurrency(showCurrency)
	nnAndroidGoods.SetSupportedPaymentCurrencies(supportCurrencySet.ToSlice())
	return nnAndroidGoods, nil
}

// 构建微信购买声明
func (q *quoteGoodsBiz) buildAppWeChatBuyStatement(language uint32, originalCCY string, rate decimal.Decimal) string {
	currencyDesc := currency.GetCurrencyDesc(language, originalCCY)
	return i18nutil.GetI18nFormatByLangCode(language, langid.StringIDWechatBuyStatementId, currencyDesc, rate.String())
}

// GetGooglePlayGoodsList moomoo android 查询商品列表
func (q *quoteGoodsBiz) GetGooglePlayGoodsList(ctx context.Context, userInfo bo.UserInfo, req *quote_goods_pb.GetGooglePlayGoodsListReq) (*quote_goods_pb.GetGooglePlayGoodsListRsp_Data, error) {
	// 兼容老版本moomoo，good_type含义不同的问题，需要映射成新的quoteGoodsId
	if platform.IsMM() {
		if newGoodsType, exist := goodstype.MooMooGoodsType2QuoteGoodsIdMap[req.GetGoodsType()]; exist {
			req.SetGoodsType(newGoodsType)
		}
	}
	quoteGoodsWithCopywriting, quoteGoodsItems, err := q.getQuoteGoodsWithItemListByGoodsId(ctx, bo.GetQuoteGoodsItemListByBo{
		UserInfo:     userInfo,
		QuoteGoodsId: req.GetGoodsType(),
		IsProUser:    req.GetIsProUser(),
	})
	if err != nil {
		return nil, err
	}
	data := q.buildDefaultGetCardGooglePlayGoodsList(req.GetGoodsType(), userInfo.Language)
	data.SetTypeDesc(quoteGoodsWithCopywriting.Title)
	data.SetMiniIconUrl(quoteGoodsWithCopywriting.MiniIconUrl)

	for _, item := range quoteGoodsItems {
		data.GoodsList = append(data.GoodsList, &quote_goods_pb.GetGooglePlayGoodsListRsp_Data_GoodsInfo{
			BuyType:         proto.Int32(int32(qt_card_enum_pb.BUY_TYPE_RENEWAL)),
			ProductId:       proto.String(item.AndroidProductId),
			GoodsDesc:       proto.String(durationenum.GetRenewDurationDesc(userInfo.Language, item.Duration)),
			RecommendLabel:  proto.String(q.getRecommendLabel(userInfo.Language, item.IsRecommend == int8(qt_card_enum_pb.IS_RECOMMEND))),
			AutoRenewedTips: proto.String(durationenum.GetAutoRenewTips(true, userInfo.Language, item.Duration, item.AndroidPrice, item.AndroidCurrency)),
			PriceInfo: &quote_goods_pb.GetGooglePlayGoodsListRsp_Data_GoodsInfo_PriceInfo{
				Price:        proto.Int32(int32(item.AndroidPrice.Mul(decimal.NewFromInt(100)).IntPart())),
				OriginPrice:  proto.Int32(int32(item.AndroidOriginalPrice.Mul(decimal.NewFromInt(100)).IntPart())),
				Currency:     proto.Int32(currency.GetCurrencyCode(item.AndroidCurrency)),
				CurrencyDesc: proto.String(currency.GetCurrencyDesc(userInfo.Language, item.AndroidCurrency)),
			},
		})
	}
	return data, nil
}

func (q *quoteGoodsBiz) buildDefaultGetCardGooglePlayGoodsList(quoteGoodsId int32, language uint32) *quote_goods_pb.GetGooglePlayGoodsListRsp_Data {
	return &quote_goods_pb.GetGooglePlayGoodsListRsp_Data{
		GoodsType:                proto.Int32(quoteGoodsId),
		TypeDesc:                 proto.String(""), // 之后赋值
		StatementDesc:            proto.String(i18nutil.GetI18nValByLangCode(language, langid.StringID194654)),
		StatementUrl:             proto.String(subscription.GetStatementUrl()),
		BuyStatement:             proto.String(i18nutil.GetI18nValByLangCode(language, langid.StringID217513)),
		AutoRenewedStatementDesc: proto.String(i18nutil.GetI18nValByLangCode(language, langid.StringID194619)),
		AutoRenewedStatementUrl:  proto.String(subscription.GetAutoRenewalStatementUrl()),
		MiniIconUrl:              proto.String(""), // 之后赋值
	}
}

// GetCardNewIapGoodsList ios查询商品列表
func (q *quoteGoodsBiz) GetCardNewIapGoodsList(ctx context.Context, userInfo bo.UserInfo, req *quote_goods_pb.GetCardNewIapGoodsListReq) (*quote_goods_pb.GetCardNewIapGoodsListRsp_IapData, error) {
	// 兼容老版本moomoo，good_type含义不同的问题，需要映射成新的quoteGoodsId
	if platform.IsMM() {
		if newGoodsType, exist := goodstype.MooMooGoodsType2QuoteGoodsIdMap[req.GetGoodsType()]; exist {
			req.SetGoodsType(newGoodsType)
		}
	}
	// trick逻辑，兼容白名单用户的购买页展示。因为白名单用户会同时展示专业和非专业卡，但因为type值一样，在购买页时无法区分，
	// 所以前端会在goods_type值后用是否专业的枚举值拼一位进行区分
	if req.GetIsProUser() == int32(qt_card_enum_pb.BOTH_PROFESSIONAL) && req.GetGoodsType() >= 10 {
		req.SetIsProUser(req.GetGoodsType() & 1)
		req.SetGoodsType(req.GetGoodsType() / 10)
	}
	quoteGoodsWithCopywriting, quoteGoodsItems, err := q.getQuoteGoodsWithItemListByGoodsId(ctx, bo.GetQuoteGoodsItemListByBo{
		UserInfo:     userInfo,
		QuoteGoodsId: req.GetGoodsType(),
		IsProUser:    req.GetIsProUser(),
	})
	if err != nil {
		return nil, err
	}
	data := q.buildDefaultGetCardNewIapGoodsList(userInfo.Language)
	data.TypeDesc = proto.String(quoteGoodsWithCopywriting.Title)
	data.MiniIconUrl = proto.String(quoteGoodsWithCopywriting.MiniIconUrl)
	goodsList := make([]*quote_goods_pb.GetCardNewIapGoodsListRsp_IapData_GoodsInfo, 0, len(quoteGoodsItems))
	for _, item := range quoteGoodsItems {
		var (
			buyType        = qt_card_enum_pb.BUY_TYPE_COMMON
			buyStatementId = qt_card_enum_pb.BUY_STATEMENT_COMMON
		)
		if item.IsIosRenewal > 0 {
			buyType = qt_card_enum_pb.BUY_TYPE_RENEWAL
			buyStatementId = qt_card_enum_pb.BUY_STATEMENT_RENAL
		}
		goodsDesc := durationenum.GetDurationDesc(userInfo.Language, item.Duration)
		if item.IsIosRenewal == int8(qt_card_enum_pb.IS_RENEWAL) {
			goodsDesc = durationenum.GetRenewDurationDesc(userInfo.Language, item.Duration)
		}
		goodsList = append(goodsList, &quote_goods_pb.GetCardNewIapGoodsListRsp_IapData_GoodsInfo{
			BuyType:         proto.Int32(int32(buyType)),
			BuyStatementId:  proto.Int32(int32(buyStatementId)),
			Type:            proto.Int32(quoteGoodsWithCopywriting.Id),
			Duration:        proto.Int32(item.Duration),
			GoodsDesc:       proto.String(goodsDesc),
			Tips:            proto.String(""),
			Label:           proto.String(q.getRecommendLabel(userInfo.Language, item.IsRecommend == int8(qt_card_enum_pb.IS_RECOMMEND))),
			SubGoodsDesc:    proto.String(""),
			AutoRenewedTips: proto.String(durationenum.GetAutoRenewTips(item.IsIosRenewal == int8(qt_card_enum_pb.IS_RENEWAL), userInfo.Language, item.Duration, item.IosPrice, item.IosCurrency)),
			PriceInfo: &quote_goods_pb.GetCardNewIapGoodsListRsp_IapData_GoodsInfo_PriceInfo{
				Id:           proto.String(strconv.Itoa(int(item.Id))),
				Price:        proto.Int32(int32(item.IosPrice.Mul(decimal.NewFromInt(100)).IntPart())),
				OriginPrice:  proto.Int32(int32(item.IosOriginalPrice.Mul(decimal.NewFromInt(100)).IntPart())),
				IapProductId: proto.String(item.IosProductId),
				ShowCurrency: proto.String(currency.GetCurrencyDesc(userInfo.Language, item.IosCurrency)),
			},
		})
	}
	data.GoodsList = goodsList
	return data, nil
}

func (q *quoteGoodsBiz) buildDefaultGetCardNewIapGoodsList(language uint32) *quote_goods_pb.GetCardNewIapGoodsListRsp_IapData {
	return &quote_goods_pb.GetCardNewIapGoodsListRsp_IapData{
		TypeDesc:      proto.String(""), // 之后赋值
		StatementDesc: proto.String(i18nutil.GetI18nValByLangCode(language, langid.StringID194654)),
		StatementUrl:  proto.String(subscription.GetStatementUrl()),
		MiniIconUrl:   proto.String(""), // 之后赋值
		BuyStatementList: []*quote_goods_pb.GetCardNewIapGoodsListRsp_IapData_BuyStatement{
			{
				Id:               proto.Int32(int32(qt_card_enum_pb.BUY_STATEMENT_COMMON)),
				BuyStatementDesc: proto.String(i18nutil.GetI18nValByLangCode(language, langid.StringID217391)),
				HightLightKey:    []string{},
				HightLightUrl:    []string{},
			},
			{
				Id:               proto.Int32(int32(qt_card_enum_pb.BUY_STATEMENT_RENAL)),
				BuyStatementDesc: proto.String(i18nutil.GetI18nValByLangCode(language, langid.StringID217394)),
				HightLightKey:    []string{i18nutil.GetI18nValByLangCode(language, langid.StringID194619), i18nutil.GetI18nValByLangCode(language, langid.StringID194620)},
				HightLightUrl:    []string{subscription.GetAutoRenewalStatementUrl(), subscription.GetDisclaimerUrl()},
			},
		},
		GoodsList: nil, // 之后赋值
	}
}

func (q *quoteGoodsBiz) getRecommendLabel(langCode uint32, isRecommend bool) string {
	if !isRecommend {
		return ""
	}
	return i18nutil.GetI18nValByLangCode(langCode, langid.StringID217524)
}

// 查询app端行情卡商品 - 文案 - item列表
func (q *quoteGoodsBiz) getQuoteGoodsWithItemListByGoodsId(ctx context.Context, goodsItemBo bo.GetQuoteGoodsItemListByBo) (*dto.QuoteGoodsWithCopywritingDto, []dbmodel.QuoteGoodsItem, error) {
	itemList := make([]dbmodel.QuoteGoodsItem, 0)
	quoteGoods, err := q.qtCardRepo.QueryOneQuoteGoodsWithCopywritingBy(ctx, dto.QueryQuoteGoodsWithCopywritingDto{
		Language:  goodsItemBo.UserInfo.Language,
		Id:        goodsItemBo.QuoteGoodsId,
		IsProUser: &goodsItemBo.IsProUser,
	}, qt_card.AppQuoteGoodsWithCopywritingQueryFields)
	if err != nil && !q.qtCardRepo.IsRecordNotFound(err) {
		metricutil.Alert("QueryAppQuoteGoodsWithCopywriting_err")
		log.Error(ctx, "QueryAppQuoteGoodsWithCopywriting_err", log.ErrorField(err), log.Int32("quoteGoodsId", goodsItemBo.QuoteGoodsId), log.Int32("isProUser", goodsItemBo.IsProUser))
		return nil, nil, errcode.ErrDBError.WithMessage("query app qupte goods with copywriting error")
	}
	if q.qtCardRepo.IsRecordNotFound(err) {
		log.Warn(ctx, "quoteGoods not found", log.Int32("quoteGoodsId", goodsItemBo.QuoteGoodsId), log.Int32("isProUser", goodsItemBo.IsProUser))
		return nil, nil, errcode.ErrNotFoundError
	}
	qo := dto.QueryQuoteGoodsItemListByDto{
		QuoteGoodsIds:   []int32{quoteGoods.Id},
		IsRenewal:       goodsItemBo.IsRenewal,
		ClientType:      &goodsItemBo.UserInfo.ClientType,
		SortOrderBy:     proto.Int32(orderby.Asc),
		DurationOrderBy: proto.Int32(orderby.Asc),
		IsProUser:       proto.Int32(goodsItemBo.IsProUser),
	}
	goodsItemList, err := q.qtCardRepo.QueryQuoteGoodsItemListBy(ctx, qo, qt_card.AppQuoteGoodsItemListQueryField)
	if err != nil {
		metricutil.Alert("QueryQuoteGoodsItemListBy_err")
		log.Error(ctx, "QueryQuoteGoodsItemListBy_err", log.ErrorField(err), log.Any("condition", qo))
		return nil, nil, errcode.ErrDBError.WithMessage("query quote goods item list error")
	}
	if len(goodsItemList) <= 0 {
		return quoteGoods, itemList, nil
	}
	// 查询过滤前置信息
	quoteGoodsItemIds := lo.Map(goodsItemList, func(item dbmodel.QuoteGoodsItem, _ int) int32 {
		return item.Id
	})
	quoteGoodsId2VersionMap, err := q.qtCardRepo.QueryVersionControlListByGoodsItemIds(ctx, quoteGoodsItemIds, platform.GetOSPlatformByClientType(goodsItemBo.UserInfo.ClientType))
	if err != nil {
		metricutil.Alert("QueryVersionControlListByQuoteGoodsIds_err")
		log.Error(ctx, "QueryVersionControlListByQuoteGoodsIds_err", log.ErrorField(err), log.Any("quoteGoodsItemIds", quoteGoodsItemIds))
		return nil, nil, err
	}
	// 查询白名单
	userIsInWhiteList := q.existInWhiteList(ctx, goodsItemBo.UserInfo.Uid)
	userCondition, err := q.getUserFilterCondition(ctx, goodsItemBo.UserInfo, userIsInWhiteList)
	if err != nil {
		metricutil.Alert("buildQuoteGoodsListFilter_err")
		log.Error(ctx, "buildQuoteGoodsListFilter_err", log.ErrorField(err))
		return nil, nil, errcode.ErrDBError.WithMessage("handle quote goods item list limit rule error")
	}
	for i, item := range goodsItemList {
		// 过滤不展示的行情卡商品
		if q.filterQuoteGoodsByRule(ctx, userCondition, bo.QuoteGoodsFilterCondition{
			WhiteListAccess:       quoteGoods.IsWhiteListAccess,
			MainBrokerFirmId:      quoteGoods.ShowInMainBrokers,
			ItemMainBrokerFirmId:  item.ShowInMainBrokers,
			ShowInUserMarkets:     usermarket.UserMarketBit(quoteGoods.ShowInUserMarkets),
			ItemShowInUserMarkets: usermarket.UserMarketBit(item.ShowInUserMarkets),
			MarketId:              int32(item.MarketId),
			QuoteGoodsId:          item.QuoteGoodsId,
			QuoteGoodsItemId:      item.Id,
			VersionCtrl:           quoteGoodsId2VersionMap[item.Id],
		}) {
			continue
		}
		itemList = append(itemList, goodsItemList[i])
	}
	return quoteGoods, itemList, nil
}

// GetQuoteGoodsCopywritingByGoodsId 根据quoteGoodsId批量查询quoteGoods多语言文案
func (q *quoteGoodsBiz) GetQuoteGoodsCopywritingByGoodsId(ctx context.Context, languageCode uint32, quoteGoodsIds []int32) (map[int32][]dbmodel.QuoteGoodsCopywriting, error) {
	queryRes, err := q.qtCardRepo.QueryQuoteGoodsCopywriting(ctx, dto.QueryQuoteGoodsCopywritingDto{
		QuoteGoodsIds: quoteGoodsIds,
		Language:      proto.Uint32(languageCode),
	}, qt_card.NormalQuoteGoodsCopywritingFields)
	if err != nil {
		return nil, err
	}
	results := lo.GroupBy(queryRes, func(item dbmodel.QuoteGoodsCopywriting) int32 {
		return item.QuoteGoodsId
	})
	return results, nil
}

// GetQuoteGoodsItemCopywritingByGoodsItemId 根据quoteGoodsId和quoteGoodsItemId 批量查询quoteGoodsItem的多语言文案
func (q *quoteGoodsBiz) GetQuoteGoodsItemCopywritingByGoodsItemId(ctx context.Context, languageCode uint32, quoteGoodsIds []int32, quoteGoodsItemIds []int32) (map[int32]dbmodel.QuoteGoodsCopywriting, error) {
	// 查询quote_goods_item
	quoteGoodsItemList, err := q.qtCardRepo.QueryQuoteGoodsItemListBy(ctx, dto.QueryQuoteGoodsItemListByDto{
		Ids: quoteGoodsItemIds,
	}, qt_card.OrderListQuoteGoodsItemFields)
	if err != nil {
		return nil, errors.WithMessage(err, "QueryQuoteGoodsItemListBy error")
	}
	if len(quoteGoodsItemList) == 0 {
		return map[int32]dbmodel.QuoteGoodsCopywriting{}, nil
	}
	goodsItemId2ItemMap := lo.SliceToMap(quoteGoodsItemList, func(item dbmodel.QuoteGoodsItem) (int32, dbmodel.QuoteGoodsItem) {
		return item.Id, item
	})
	// 优先拿入参的quoteGoodsIds，拿不到才拿查询到的quote_goods_item里的
	if len(quoteGoodsIds) == 0 {
		quoteGoodsIds = lo.Map(quoteGoodsItemList, func(item dbmodel.QuoteGoodsItem, index int) int32 {
			return item.QuoteGoodsId
		})
	}
	// 查询quote_goods的文案
	quoteGoodsId2DescListMap, err := q.GetQuoteGoodsCopywritingByGoodsId(ctx, languageCode, quoteGoodsIds)
	if err != nil {
		return nil, errors.WithMessage(err, "GetQuoteGoodsCopywritingByGoodsId error")
	}
	// 关联商品描述
	quoteGoodsItemId2DescMap := make(map[int32]dbmodel.QuoteGoodsCopywriting, len(quoteGoodsId2DescListMap))
	for goodsItemId, item := range goodsItemId2ItemMap {
		descList := quoteGoodsId2DescListMap[item.QuoteGoodsId]
		for _, descDetail := range descList {
			if descDetail.IsProfessional == item.IsProfessional &&
				descDetail.UserTypeSupport == item.UserTypeSupport {
				quoteGoodsItemId2DescMap[goodsItemId] = descDetail
			}
		}
	}
	return quoteGoodsItemId2DescMap, nil
}

func (q *quoteGoodsBiz) GetInAppQuoteGoodsItemFullInfoMap(ctx context.Context, clientType uint32) (map[string]bo.QuoteGoodsItemFullInfoBo, error) {
	productIDsData, err := q.GetPlatformProductIdList(ctx, bo.UserInfo{ClientType: clientType}, nil)
	if err != nil {
		logutil.Error(ctx, "GetPlatformProductIdList_err", log.ErrorField(err)).WithAlert()
		return nil, err
	}

	quoteGoodsItems, err := q.qtCardRepo.QueryQuoteGoodsItemListByProductIDs(ctx, productIDsData.GetAllProductionIDList(), clientType)
	if err != nil {
		return nil, errors.WithMessage(err, "QueryQuoteGoodsItemListByProductIDs error")
	}
	result := make(map[string]bo.QuoteGoodsItemFullInfoBo, len(quoteGoodsItems))
	var quoteGoodsIds []int32
	for _, item := range quoteGoodsItems {
		quoteGoodsIds = append(quoteGoodsIds, item.QuoteGoodsId)
	}

	goodsCopywritingRes, err := q.qtCardRepo.QueryQuoteGoodsCopywriting(ctx, dto.QueryQuoteGoodsCopywritingDto{
		QuoteGoodsIds:  quoteGoodsIds,
		IsProfessional: proto.Int32(int32(qt_card_enum_pb.IS_NOT_PROFESSIONAL)),
	}, qt_card.NormalQuoteGoodsCopywritingFields)
	if err != nil {
		metricutil.Alert("QueryQuoteGoodsCopywriting_err")
		log.Error(ctx, "QueryQuoteGoodsCopywriting_err", log.ErrorField(err))
		return nil, err
	}

	quoteGoodsID2QuoteTypeIDsMap, err := q.getGoodsType2QuoteTypeIDMapping(ctx)
	if err != nil {
		return nil, err
	}

	goodsCopywritingGroup := lo.GroupBy(goodsCopywritingRes, func(item dbmodel.QuoteGoodsCopywriting) int32 {
		return item.QuoteGoodsId
	})
	for productID, item := range quoteGoodsItems {
		var (
			ccy   string
			price decimal.Decimal
		)
		switch clientType {
		case metadata.ClientTypeMMAndroid, metadata.ClientTypeNNAndroid:
			ccy = item.AndroidCurrency
			price = item.AndroidPrice
		case metadata.ClientTypeMMIOS, metadata.ClientTypeNNIOS:
			ccy = item.IosCurrency
			price = item.IosPrice
		}

		result[productID] = bo.QuoteGoodsItemFullInfoBo{
			QuoteGoodsID:     item.QuoteGoodsId,
			QuoteGoodsItemID: item.Id,
			QuoteTypeIDs:     quoteGoodsID2QuoteTypeIDsMap[item.QuoteGoodsId],
			ProductID:        productID,
			Duration:         item.Duration,
			Currency:         ccy,
			Price:            price,
			Copywritings: lo.KeyBy(goodsCopywritingGroup[item.QuoteGoodsId], func(item dbmodel.QuoteGoodsCopywriting) int32 {
				return int32(item.Language)
			}),
		}
	}
	return result, nil
}

// 获取quoteGoodsID与quoteTypeID的映射关系, quoteGoodsID -> []quoteTypeID
func (q *quoteGoodsBiz) getGoodsType2QuoteTypeIDMapping(ctx context.Context) (map[int32][]int32, error) {
	typeMaps, err := q.qtCardRepo.QueryAllTypeMap(ctx)
	if err != nil {
		logutil.Error(ctx, "QueryAllTypeMap_err", log.ErrorField(err)).WithAlert()
		return nil, err
	}
	// 按quoteGoodsID分组
	typeMapsGroup := lo.GroupBy(typeMaps, func(item dbmodel.TypeMap) int32 {
		return item.Type
	})

	result := make(map[int32][]int32)
	for quoteGoodsID, typeMapsList := range typeMapsGroup {
		result[quoteGoodsID] = lo.Map(typeMapsList, func(item dbmodel.TypeMap, index int) int32 {
			return item.QuoteTypeID
		})
	}
	return result, nil
}
